package com.bilibili.miniapp.open.portal.vo.settlement;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票上传响应VO
 *
 * <AUTHOR>
 * @date 2025/5/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InvoiceUploadResultVo {

    /**
     * 汇联易上传后返回的oid
     */
    private String oid;

    /**
     * BFS上传后返回的url
     */
    private String url;
}
